{"version":3,"file":"index.js","sources":["../src/util/location.ts","../src/parse-error/module-errors.ts","../src/parse-error/to-node-description.ts","../src/parse-error/standard-errors.ts","../src/parse-error/strict-mode-errors.ts","../src/parse-error/parse-expression-errors.ts","../src/parse-error/pipeline-operator-errors.ts","../src/parse-error.ts","../src/options.ts","../src/plugins/estree.ts","../src/tokenizer/context.ts","../src/tokenizer/types.ts","../../babel-helper-validator-identifier/src/identifier.ts","../../babel-helper-validator-identifier/src/keyword.ts","../src/util/identifier.ts","../src/util/scope.ts","../src/plugins/flow/scope.ts","../src/plugins/flow/index.ts","../src/plugins/jsx/xhtml.ts","../src/util/whitespace.ts","../src/plugins/jsx/index.ts","../src/plugins/typescript/scope.ts","../src/util/production-parameter.ts","../src/parser/base.ts","../src/parser/comments.ts","../src/tokenizer/state.ts","../../babel-helper-string-parser/src/index.ts","../src/tokenizer/index.ts","../src/util/class-scope.ts","../src/util/expression-scope.ts","../src/parser/util.ts","../src/parser/node.ts","../src/parser/lval.ts","../src/plugins/typescript/index.ts","../src/plugins/placeholders.ts","../src/plugins/v8intrinsic.ts","../src/plugin-utils.ts","../src/parser/expression.ts","../src/parser/statement.ts","../src/parser/index.ts","../src/index.ts"],"sourcesContent":["export type Pos = {\n  start: number;\n};\n\n// These are used when `options.locations` is on, for the\n// `startLoc` and `endLoc` properties.\n\nexport class Position {\n  line: number;\n  column: number;\n  index: number;\n\n  constructor(line: number, col: number, index: number) {\n    this.line = line;\n    this.column = col;\n    this.index = index;\n  }\n}\n\nexport class SourceLocation {\n  start: Position;\n  end: Position;\n  filename: string;\n  identifierName: string | undefined | null;\n\n  constructor(start: Position, end?: Position) {\n    this.start = start;\n    // (may start as null, but initialized later)\n    this.end = end;\n  }\n}\n\n/**\n * creates a new position with a non-zero column offset from the given position.\n * This function should be only be used when we create AST node out of the token\n * boundaries, such as TemplateElement ends before tt.templateNonTail. This\n * function does not skip whitespaces.\n */\nexport function createPositionWithColumnOffset(\n  position: Position,\n  columnOffset: number,\n) {\n  const { line, column, index } = position;\n  return new Position(line, column + columnOffset, index + columnOffset);\n}\n","import type { ParseErrorTemplates } from \"../parse-error.ts\";\n\nconst code = \"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\";\n\nexport default {\n  ImportMetaOutsideModule: {\n    message: `import.meta may appear only with 'sourceType: \"module\"'`,\n    code,\n  },\n  ImportOutsideModule: {\n    message: `'import' and 'export' may appear only with 'sourceType: \"module\"'`,\n    code,\n  },\n} satisfies ParseErrorTemplates;\n","const NodeDescriptions = {\n  ArrayPattern: \"array destructuring pattern\",\n  AssignmentExpression: \"assignment expression\",\n  AssignmentPattern: \"assignment expression\",\n  ArrowFunctionExpression: \"arrow function expression\",\n  ConditionalExpression: \"conditional expression\",\n  CatchClause: \"catch clause\",\n  ForOfStatement: \"for-of statement\",\n  ForInStatement: \"for-in statement\",\n  ForStatement: \"for-loop\",\n  FormalParameters: \"function parameter list\",\n  Identifier: \"identifier\",\n  ImportSpecifier: \"import specifier\",\n  ImportDefaultSpecifier: \"import default specifier\",\n  ImportNamespaceSpecifier: \"import namespace specifier\",\n  ObjectPattern: \"object destructuring pattern\",\n  ParenthesizedExpression: \"parenthesized expression\",\n  RestElement: \"rest element\",\n  UpdateExpression: {\n    true: \"prefix operation\",\n    false: \"postfix operation\",\n  },\n  VariableDeclarator: \"variable declaration\",\n  YieldExpression: \"yield expression\",\n};\n\ntype NodeTypesWithDescriptions = keyof Omit<\n  typeof NodeDescriptions,\n  \"UpdateExpression\"\n>;\n\ntype NodeWithDescription =\n  | {\n      type: \"UpdateExpression\";\n      prefix: boolean;\n    }\n  | {\n      type: NodeTypesWithDescriptions;\n    };\n\nconst toNodeDescription = (node: NodeWithDescription) =>\n  node.type === \"UpdateExpression\"\n    ? NodeDescriptions.UpdateExpression[`${node.prefix}`]\n    : NodeDescriptions[node.type];\n\nexport default toNodeDescription;\n","import type { ParseErrorTemplates } from \"../parse-error.ts\";\nimport toNodeDescription from \"./to-node-description.ts\";\n\nexport type LValAncestor =\n  | { type: \"UpdateExpression\"; prefix: boolean }\n  | {\n      type:\n        | \"ArrayPattern\"\n        | \"AssignmentExpression\"\n        | \"CatchClause\"\n        | \"ForOfStatement\"\n        | \"FormalParameters\"\n        | \"ForInStatement\"\n        | \"ForStatement\"\n        | \"ImportSpecifier\"\n        | \"ImportNamespaceSpecifier\"\n        | \"ImportDefaultSpecifier\"\n        | \"ParenthesizedExpression\"\n        | \"ObjectPattern\"\n        | \"RestElement\"\n        | \"VariableDeclarator\";\n    };\n\nexport default {\n  AccessorIsGenerator: ({ kind }: { kind: \"get\" | \"set\" }) =>\n    `A ${kind}ter cannot be a generator.`,\n  ArgumentsInClass:\n    \"'arguments' is only allowed in functions and class methods.\",\n  AsyncFunctionInSingleStatementContext:\n    \"Async functions can only be declared at the top level or inside a block.\",\n  AwaitBindingIdentifier:\n    \"Can not use 'await' as identifier inside an async function.\",\n  AwaitBindingIdentifierInStaticBlock:\n    \"Can not use 'await' as identifier inside a static block.\",\n  AwaitExpressionFormalParameter:\n    \"'await' is not allowed in async function parameters.\",\n  AwaitUsingNotInAsyncContext:\n    \"'await using' is only allowed within async functions and at the top levels of modules.\",\n  AwaitNotInAsyncContext:\n    \"'await' is only allowed within async functions and at the top levels of modules.\",\n  BadGetterArity: \"A 'get' accessor must not have any formal parameters.\",\n  BadSetterArity: \"A 'set' accessor must have exactly one formal parameter.\",\n  BadSetterRestParameter:\n    \"A 'set' accessor function argument must not be a rest parameter.\",\n  ConstructorClassField: \"Classes may not have a field named 'constructor'.\",\n  ConstructorClassPrivateField:\n    \"Classes may not have a private field named '#constructor'.\",\n  ConstructorIsAccessor: \"Class constructor may not be an accessor.\",\n  ConstructorIsAsync: \"Constructor can't be an async function.\",\n  ConstructorIsGenerator: \"Constructor can't be a generator.\",\n  DeclarationMissingInitializer: ({\n    kind,\n  }: {\n    kind: \"await using\" | \"const\" | \"destructuring\" | \"using\";\n  }) => `Missing initializer in ${kind} declaration.`,\n  DecoratorArgumentsOutsideParentheses:\n    \"Decorator arguments must be moved inside parentheses: use '@(decorator(args))' instead of '@(decorator)(args)'.\",\n  DecoratorBeforeExport:\n    \"Decorators must be placed *before* the 'export' keyword. Remove the 'decoratorsBeforeExport: true' option to use the 'export @decorator class {}' syntax.\",\n  DecoratorsBeforeAfterExport:\n    \"Decorators can be placed *either* before or after the 'export' keyword, but not in both locations at the same time.\",\n  DecoratorConstructor:\n    \"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?\",\n  DecoratorExportClass:\n    \"Decorators must be placed *after* the 'export' keyword. Remove the 'decoratorsBeforeExport: false' option to use the '@decorator export class {}' syntax.\",\n  DecoratorSemicolon: \"Decorators must not be followed by a semicolon.\",\n  DecoratorStaticBlock: \"Decorators can't be used with a static block.\",\n  DeferImportRequiresNamespace:\n    'Only `import defer * as x from \"./module\"` is valid.',\n  DeletePrivateField: \"Deleting a private field is not allowed.\",\n  DestructureNamedImport:\n    \"ES2015 named imports do not destructure. Use another statement for destructuring after the import.\",\n  DuplicateConstructor: \"Duplicate constructor in the same class.\",\n  DuplicateDefaultExport: \"Only one default export allowed per module.\",\n  DuplicateExport: ({ exportName }: { exportName: string }) =>\n    `\\`${exportName}\\` has already been exported. Exported identifiers must be unique.`,\n  DuplicateProto: \"Redefinition of __proto__ property.\",\n  DuplicateRegExpFlags: \"Duplicate regular expression flag.\",\n  ElementAfterRest: \"Rest element must be last element.\",\n  EscapedCharNotAnIdentifier: \"Invalid Unicode escape.\",\n  ExportBindingIsString: ({\n    localName,\n    exportName,\n  }: {\n    localName: string;\n    exportName: string;\n  }) =>\n    `A string literal cannot be used as an exported binding without \\`from\\`.\\n- Did you mean \\`export { '${localName}' as '${exportName}' } from 'some-module'\\`?`,\n  ExportDefaultFromAsIdentifier:\n    \"'from' is not allowed as an identifier after 'export default'.\",\n\n  ForInOfLoopInitializer: ({\n    type,\n  }: {\n    type: \"ForInStatement\" | \"ForOfStatement\";\n  }) =>\n    `'${\n      type === \"ForInStatement\" ? \"for-in\" : \"for-of\"\n    }' loop variable declaration may not have an initializer.`,\n  ForInUsing: \"For-in loop may not start with 'using' declaration.\",\n\n  ForOfAsync: \"The left-hand side of a for-of loop may not be 'async'.\",\n  ForOfLet: \"The left-hand side of a for-of loop may not start with 'let'.\",\n  GeneratorInSingleStatementContext:\n    \"Generators can only be declared at the top level or inside a block.\",\n\n  IllegalBreakContinue: ({\n    type,\n  }: {\n    type: \"BreakStatement\" | \"ContinueStatement\";\n  }) => `Unsyntactic ${type === \"BreakStatement\" ? \"break\" : \"continue\"}.`,\n\n  IllegalLanguageModeDirective:\n    \"Illegal 'use strict' directive in function with non-simple parameter list.\",\n  IllegalReturn: \"'return' outside of function.\",\n  ImportAttributesUseAssert:\n    \"The `assert` keyword in import attributes is deprecated and it has been replaced by the `with` keyword. You can enable the `deprecatedImportAssert` parser plugin to suppress this error.\",\n  ImportBindingIsString: ({ importName }: { importName: string }) =>\n    `A string literal cannot be used as an imported binding.\\n- Did you mean \\`import { \"${importName}\" as foo }\\`?`,\n  ImportCallArity: `\\`import()\\` requires exactly one or two arguments.`,\n  ImportCallNotNewExpression: \"Cannot use new with import(...).\",\n  ImportCallSpreadArgument: \"`...` is not allowed in `import()`.\",\n  ImportJSONBindingNotDefault:\n    \"A JSON module can only be imported with `default`.\",\n  ImportReflectionHasAssertion: \"`import module x` cannot have assertions.\",\n  ImportReflectionNotBinding:\n    'Only `import module x from \"./module\"` is valid.',\n  IncompatibleRegExpUVFlags:\n    \"The 'u' and 'v' regular expression flags cannot be enabled at the same time.\",\n  InvalidBigIntLiteral: \"Invalid BigIntLiteral.\",\n  InvalidCodePoint: \"Code point out of bounds.\",\n  InvalidCoverInitializedName: \"Invalid shorthand property initializer.\",\n  InvalidDecimal: \"Invalid decimal.\",\n  InvalidDigit: ({ radix }: { radix: number }) =>\n    `Expected number in radix ${radix}.`,\n  InvalidEscapeSequence: \"Bad character escape sequence.\",\n  InvalidEscapeSequenceTemplate: \"Invalid escape sequence in template.\",\n  InvalidEscapedReservedWord: ({ reservedWord }: { reservedWord: string }) =>\n    `Escape sequence in keyword ${reservedWord}.`,\n  InvalidIdentifier: ({ identifierName }: { identifierName: string }) =>\n    `Invalid identifier ${identifierName}.`,\n  InvalidLhs: ({ ancestor }: { ancestor: LValAncestor }) =>\n    `Invalid left-hand side in ${toNodeDescription(ancestor)}.`,\n  InvalidLhsBinding: ({ ancestor }: { ancestor: LValAncestor }) =>\n    `Binding invalid left-hand side in ${toNodeDescription(ancestor)}.`,\n  InvalidLhsOptionalChaining: ({ ancestor }: { ancestor: LValAncestor }) =>\n    `Invalid optional chaining in the left-hand side of ${toNodeDescription(\n      ancestor,\n    )}.`,\n  InvalidNumber: \"Invalid number.\",\n  InvalidOrMissingExponent:\n    \"Floating-point numbers require a valid exponent after the 'e'.\",\n  InvalidOrUnexpectedToken: ({ unexpected }: { unexpected: string }) =>\n    `Unexpected character '${unexpected}'.`,\n  InvalidParenthesizedAssignment: \"Invalid parenthesized assignment pattern.\",\n  InvalidPrivateFieldResolution: ({\n    identifierName,\n  }: {\n    identifierName: string;\n  }) => `Private name #${identifierName} is not defined.`,\n  InvalidPropertyBindingPattern: \"Binding member expression.\",\n  InvalidRecordProperty:\n    \"Only properties and spread elements are allowed in record definitions.\",\n  InvalidRestAssignmentPattern: \"Invalid rest operator's argument.\",\n  LabelRedeclaration: ({ labelName }: { labelName: string }) =>\n    `Label '${labelName}' is already declared.`,\n  LetInLexicalBinding: \"'let' is disallowed as a lexically bound name.\",\n  LineTerminatorBeforeArrow: \"No line break is allowed before '=>'.\",\n  MalformedRegExpFlags: \"Invalid regular expression flag.\",\n  MissingClassName: \"A class name is required.\",\n  MissingEqInAssignment:\n    \"Only '=' operator can be used for specifying default value.\",\n  MissingSemicolon: \"Missing semicolon.\",\n  MissingPlugin: ({ missingPlugin }: { missingPlugin: [string] }) =>\n    `This experimental syntax requires enabling the parser plugin: ${missingPlugin\n      .map(name => JSON.stringify(name))\n      .join(\", \")}.`,\n  // FIXME: Would be nice to make this \"missingPlugins\" instead.\n  // Also, seems like we can drop the \"(s)\" from the message and just make it \"s\".\n  MissingOneOfPlugins: ({ missingPlugin }: { missingPlugin: string[] }) =>\n    `This experimental syntax requires enabling one of the following parser plugin(s): ${missingPlugin\n      .map(name => JSON.stringify(name))\n      .join(\", \")}.`,\n  MissingUnicodeEscape: \"Expecting Unicode escape sequence \\\\uXXXX.\",\n  MixingCoalesceWithLogical:\n    \"Nullish coalescing operator(??) requires parens when mixing with logical operators.\",\n  ModuleAttributeDifferentFromType:\n    \"The only accepted module attribute is `type`.\",\n  ModuleAttributeInvalidValue:\n    \"Only string literals are allowed as module attribute values.\",\n  ModuleAttributesWithDuplicateKeys: ({ key }: { key: string }) =>\n    `Duplicate key \"${key}\" is not allowed in module attributes.`,\n  ModuleExportNameHasLoneSurrogate: ({\n    surrogateCharCode,\n  }: {\n    surrogateCharCode: number;\n  }) =>\n    `An export name cannot include a lone surrogate, found '\\\\u${surrogateCharCode.toString(\n      16,\n    )}'.`,\n  ModuleExportUndefined: ({ localName }: { localName: string }) =>\n    `Export '${localName}' is not defined.`,\n  MultipleDefaultsInSwitch: \"Multiple default clauses.\",\n  NewlineAfterThrow: \"Illegal newline after throw.\",\n  NoCatchOrFinally: \"Missing catch or finally clause.\",\n  NumberIdentifier: \"Identifier directly after number.\",\n  NumericSeparatorInEscapeSequence:\n    \"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.\",\n  ObsoleteAwaitStar:\n    \"'await*' has been removed from the async functions proposal. Use Promise.all() instead.\",\n  OptionalChainingNoNew:\n    \"Constructors in/after an Optional Chain are not allowed.\",\n  OptionalChainingNoTemplate:\n    \"Tagged Template Literals are not allowed in optionalChain.\",\n  OverrideOnConstructor:\n    \"'override' modifier cannot appear on a constructor declaration.\",\n  ParamDupe: \"Argument name clash.\",\n  PatternHasAccessor: \"Object pattern can't contain getter or setter.\",\n  PatternHasMethod: \"Object pattern can't contain methods.\",\n  PrivateInExpectedIn: ({ identifierName }: { identifierName: string }) =>\n    `Private names are only allowed in property accesses (\\`obj.#${identifierName}\\`) or in \\`in\\` expressions (\\`#${identifierName} in obj\\`).`,\n  PrivateNameRedeclaration: ({ identifierName }: { identifierName: string }) =>\n    `Duplicate private name #${identifierName}.`,\n  RecordExpressionBarIncorrectEndSyntaxType:\n    \"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  RecordExpressionBarIncorrectStartSyntaxType:\n    \"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  RecordExpressionHashIncorrectStartSyntaxType:\n    \"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",\n  RecordNoProto: \"'__proto__' is not allowed in Record expressions.\",\n  RestTrailingComma: \"Unexpected trailing comma after rest element.\",\n  SloppyFunction:\n    \"In non-strict mode code, functions can only be declared at top level or inside a block.\",\n  SloppyFunctionAnnexB:\n    \"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.\",\n  SourcePhaseImportRequiresDefault:\n    'Only `import source x from \"./module\"` is valid.',\n  StaticPrototype: \"Classes may not have static property named prototype.\",\n  SuperNotAllowed:\n    \"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?\",\n  SuperPrivateField: \"Private fields can't be accessed on super.\",\n  TrailingDecorator: \"Decorators must be attached to a class element.\",\n  TupleExpressionBarIncorrectEndSyntaxType:\n    \"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  TupleExpressionBarIncorrectStartSyntaxType:\n    \"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.\",\n  TupleExpressionHashIncorrectStartSyntaxType:\n    \"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.\",\n  UnexpectedArgumentPlaceholder: \"Unexpected argument placeholder.\",\n  UnexpectedAwaitAfterPipelineBody:\n    'Unexpected \"await\" after pipeline body; await must have parentheses in minimal proposal.',\n  UnexpectedDigitAfterHash: \"Unexpected digit after hash token.\",\n  UnexpectedImportExport:\n    \"'import' and 'export' may only appear at the top level.\",\n  UnexpectedKeyword: ({ keyword }: { keyword: string }) =>\n    `Unexpected keyword '${keyword}'.`,\n  UnexpectedLeadingDecorator:\n    \"Leading decorators must be attached to a class declaration.\",\n  UnexpectedLexicalDeclaration:\n    \"Lexical declaration cannot appear in a single-statement context.\",\n  UnexpectedNewTarget:\n    \"`new.target` can only be used in functions or class properties.\",\n  UnexpectedNumericSeparator:\n    \"A numeric separator is only allowed between two digits.\",\n  UnexpectedPrivateField: \"Unexpected private name.\",\n  UnexpectedReservedWord: ({ reservedWord }: { reservedWord: string }) =>\n    `Unexpected reserved word '${reservedWord}'.`,\n  UnexpectedSuper: \"'super' is only allowed in object methods and classes.\",\n  UnexpectedToken: ({\n    expected,\n    unexpected,\n  }: {\n    expected?: string | null;\n    unexpected?: string | null;\n  }) =>\n    `Unexpected token${unexpected ? ` '${unexpected}'.` : \"\"}${\n      expected ? `, expected \"${expected}\"` : \"\"\n    }`,\n  UnexpectedTokenUnaryExponentiation:\n    \"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.\",\n  UnexpectedUsingDeclaration:\n    \"Using declaration cannot appear in the top level when source type is `script` or in the bare case statement.\",\n  UnsupportedBind: \"Binding should be performed on object property.\",\n  UnsupportedDecoratorExport:\n    \"A decorated export must export a class declaration.\",\n  UnsupportedDefaultExport:\n    \"Only expressions, functions or classes are allowed as the `default` export.\",\n  UnsupportedImport:\n    \"`import` can only be used in `import()` or `import.meta`.\",\n  UnsupportedMetaProperty: ({\n    target,\n    onlyValidPropertyName,\n  }: {\n    target: string;\n    onlyValidPropertyName: string;\n  }) =>\n    `The only valid meta property for ${target} is ${target}.${onlyValidPropertyName}.`,\n  UnsupportedParameterDecorator:\n    \"Decorators cannot be used to decorate parameters.\",\n  UnsupportedPropertyDecorator:\n    \"Decorators cannot be used to decorate object literal properties.\",\n  UnsupportedSuper:\n    \"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).\",\n  UnterminatedComment: \"Unterminated comment.\",\n  UnterminatedRegExp: \"Unterminated regular expression.\",\n  UnterminatedString: \"Unterminated string constant.\",\n  UnterminatedTemplate: \"Unterminated template.\",\n  UsingDeclarationExport: \"Using declaration cannot be exported.\",\n  UsingDeclarationHasBindingPattern:\n    \"Using declaration cannot have destructuring patterns.\",\n  VarRedeclaration: ({ identifierName }: { identifierName: string }) =>\n    `Identifier '${identifierName}' has already been declared.`,\n  YieldBindingIdentifier:\n    \"Can not use 'yield' as identifier inside a generator.\",\n  YieldInParameter: \"Yield expression is not allowed in formal parameters.\",\n  YieldNotInGeneratorFunction:\n    \"'yield' is only allowed within generator functions.\",\n  ZeroDigitNumericSeparator:\n    \"Numeric separator can not be used after leading 0.\",\n} satisfies ParseErrorTemplates;\n","import type { ParseErrorTemplates } from \"../parse-error\";\n\nexport default {\n  StrictDelete: \"Deleting local variable in strict mode.\",\n\n  // `referenceName` is the StringValue[1] of an IdentifierReference[2], which\n  // is represented as just an `Identifier`[3] in the Babel AST.\n  // 1. https://tc39.es/ecma262/#sec-static-semantics-stringvalue\n  // 2. https://tc39.es/ecma262/#prod-IdentifierReference\n  // 3. https://github.com/babel/babel/blob/main/packages/babel-parser/ast/spec.md#identifier\n  StrictEvalArguments: ({ referenceName }: { referenceName: string }) =>\n    `Assigning to '${referenceName}' in strict mode.`,\n\n  // `bindingName` is the StringValue[1] of a BindingIdentifier[2], which is\n  // represented as just an `Identifier`[3] in the Babel AST.\n  // 1. https://tc39.es/ecma262/#sec-static-semantics-stringvalue\n  // 2. https://tc39.es/ecma262/#prod-BindingIdentifier\n  // 3. https://github.com/babel/babel/blob/main/packages/babel-parser/ast/spec.md#identifier\n  StrictEvalArgumentsBinding: ({ bindingName }: { bindingName: string }) =>\n    `Binding '${bindingName}' in strict mode.`,\n\n  StrictFunction:\n    \"In strict mode code, functions can only be declared at top level or inside a block.\",\n\n  StrictNumericEscape: \"The only valid numeric escape in strict mode is '\\\\0'.\",\n\n  StrictOctalLiteral: \"Legacy octal literals are not allowed in strict mode.\",\n\n  StrictWith: \"'with' in strict mode.\",\n} satisfies ParseErrorTemplates;\n","import type { ParseErrorTemplates } from \"../parse-error.ts\";\n\nexport default {\n  ParseExpressionEmptyInput:\n    \"Unexpected parseExpression() input: The input is empty or contains only comments.\",\n  ParseExpressionExpectsEOF: ({ unexpected }: { unexpected: number }) =>\n    `Unexpected parseExpression() input: The input should contain exactly one expression, but the first expression is followed by the unexpected character \\`${String.fromCodePoint(unexpected)}\\`.`,\n} satisfies ParseErrorTemplates;\n","import type { ParseErrorTemplates } from \"../parse-error.ts\";\nimport toNodeDescription from \"./to-node-description.ts\";\n\nexport const UnparenthesizedPipeBodyDescriptions = new Set([\n  \"ArrowFunctionExpression\",\n  \"AssignmentExpression\",\n  \"ConditionalExpression\",\n  \"YieldExpression\",\n] as const);\n\ntype GetSetMemberType<T extends Set<any>> =\n  T extends Set<infer M> ? M : unknown;\n\nexport type UnparenthesizedPipeBodyTypes = GetSetMemberType<\n  typeof UnparenthesizedPipeBodyDescriptions\n>;\n\nexport default {\n  // This error is only used by the smart-mix proposal\n  PipeBodyIsTighter:\n    \"Unexpected yield after pipeline body; any yield expression acting as Hack-style pipe body must be parenthesized due to its loose operator precedence.\",\n  PipeTopicRequiresHackPipes: process.env.BABEL_8_BREAKING\n    ? 'Topic references are only supported when using the `\"proposal\": \"hack\"` version of the pipeline proposal.'\n    : 'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.',\n  PipeTopicUnbound:\n    \"Topic reference is unbound; it must be inside a pipe body.\",\n  PipeTopicUnconfiguredToken: ({ token }: { token: string }) =>\n    `Invalid topic token ${token}. In order to use ${token} as a topic reference, the pipelineOperator plugin must be configured with { \"proposal\": \"hack\", \"topicToken\": \"${token}\" }.`,\n  PipeTopicUnused:\n    \"Hack-style pipe body does not contain a topic reference; Hack-style pipes must use topic at least once.\",\n  PipeUnparenthesizedBody: ({ type }: { type: UnparenthesizedPipeBodyTypes }) =>\n    `Hack-style pipe body cannot be an unparenthesized ${toNodeDescription({\n      type,\n    })}; please wrap it in parentheses.`,\n\n  ...(process.env.BABEL_8_BREAKING\n    ? {}\n    : {\n        // Messages whose codes start with “Pipeline” or “PrimaryTopic”\n        // are retained for backwards compatibility\n        // with the deprecated smart-mix pipe operator proposal plugin.\n        // They are subject to removal in a future major version.\n        PipelineBodyNoArrow:\n          'Unexpected arrow \"=>\" after pipeline body; arrow function in pipeline body must be parenthesized.',\n        PipelineBodySequenceExpression:\n          \"Pipeline body may not be a comma-separated sequence expression.\",\n        PipelineHeadSequenceExpression:\n          \"Pipeline head should not be a comma-separated sequence expression.\",\n        PipelineTopicUnused:\n          \"Pipeline is in topic style but does not use topic reference.\",\n        PrimaryTopicNotAllowed:\n          \"Topic reference was used in a lexical context without topic binding.\",\n        PrimaryTopicRequiresSmartPipeline:\n          'Topic reference is used, but the pipelineOperator plugin was not passed a \"proposal\": \"hack\" or \"smart\" option.',\n      }),\n} satisfies ParseErrorTemplates;\n","import { Position } from \"./util/location.ts\";\n\ntype SyntaxPlugin =\n  | \"flow\"\n  | \"typescript\"\n  | \"jsx\"\n  | \"pipelineOperator\"\n  | \"placeholders\";\n\ntype ParseErrorCode =\n  | \"BABEL_PARSER_SYNTAX_ERROR\"\n  | \"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\";\n\n// Babel uses \"normal\" SyntaxErrors for it's errors, but adds some extra\n// functionality. This functionality is defined in the\n// `ParseErrorSpecification` interface below. We may choose to change to someday\n// give our errors their own full-blown class, but until then this allow us to\n// keep all the desirable properties of SyntaxErrors (like their name in stack\n// traces, etc.), and also allows us to punt on any publicly facing\n// class-hierarchy decisions until Babel 8.\ninterface ParseErrorSpecification<ErrorDetails> {\n  // Look, these *could* be readonly, but then Flow complains when we initially\n  // set them. We could do a whole dance and make a special interface that's not\n  // readonly for when we create the error, then cast it to the readonly\n  // interface for public use, but the previous implementation didn't have them\n  // as readonly, so let's just not worry about it for now.\n  code: ParseErrorCode;\n  reasonCode: string;\n  syntaxPlugin?: SyntaxPlugin;\n  missingPlugin?: string | string[];\n  loc: Position;\n  details: ErrorDetails;\n\n  // We should consider removing this as it now just contains the same\n  // information as `loc.index`.\n  pos: number;\n}\n\nexport type ParseError<ErrorDetails> = SyntaxError &\n  ParseErrorSpecification<ErrorDetails>;\n\n// By `ParseErrorConstructor`, we mean something like the new-less style\n// `ErrorConstructor`[1], since `ParseError`'s are not themselves actually\n// separate classes from `SyntaxError`'s.\n//\n// 1. https://github.com/microsoft/TypeScript/blob/v4.5.5/lib/lib.es5.d.ts#L1027\nexport type ParseErrorConstructor<ErrorDetails> = (\n  loc: Position,\n  details: ErrorDetails,\n) => ParseError<ErrorDetails>;\n\ntype ToMessage<ErrorDetails> = (self: ErrorDetails) => string;\n\ntype ParseErrorCredentials<ErrorDetails> = {\n  code: string;\n  reasonCode: string;\n  syntaxPlugin?: SyntaxPlugin;\n  toMessage: ToMessage<ErrorDetails>;\n};\n\nfunction defineHidden(obj: object, key: string, value: unknown) {\n  Object.defineProperty(obj, key, {\n    enumerable: false,\n    configurable: true,\n    value,\n  });\n}\n\nfunction toParseErrorConstructor<ErrorDetails extends object>({\n  toMessage,\n  code,\n  reasonCode,\n  syntaxPlugin,\n}: ParseErrorCredentials<ErrorDetails>): ParseErrorConstructor<ErrorDetails> {\n  const hasMissingPlugin =\n    reasonCode === \"MissingPlugin\" || reasonCode === \"MissingOneOfPlugins\";\n\n  if (!process.env.BABEL_8_BREAKING) {\n    const oldReasonCodes: Record<string, string> = {\n      AccessorCannotDeclareThisParameter: \"AccesorCannotDeclareThisParameter\",\n      AccessorCannotHaveTypeParameters: \"AccesorCannotHaveTypeParameters\",\n      ConstInitializerMustBeStringOrNumericLiteralOrLiteralEnumReference:\n        \"ConstInitiailizerMustBeStringOrNumericLiteralOrLiteralEnumReference\",\n      SetAccessorCannotHaveOptionalParameter:\n        \"SetAccesorCannotHaveOptionalParameter\",\n      SetAccessorCannotHaveRestParameter: \"SetAccesorCannotHaveRestParameter\",\n      SetAccessorCannotHaveReturnType: \"SetAccesorCannotHaveReturnType\",\n    };\n    if (oldReasonCodes[reasonCode]) {\n      reasonCode = oldReasonCodes[reasonCode];\n    }\n  }\n\n  return function constructor(loc: Position, details: ErrorDetails) {\n    const error: ParseError<ErrorDetails> = new SyntaxError() as any;\n\n    error.code = code as ParseErrorCode;\n    error.reasonCode = reasonCode;\n    error.loc = loc;\n    error.pos = loc.index;\n\n    error.syntaxPlugin = syntaxPlugin;\n    if (hasMissingPlugin) {\n      error.missingPlugin = (details as any).missingPlugin;\n    }\n\n    type Overrides = {\n      loc?: Position;\n      details?: ErrorDetails;\n    };\n    defineHidden(error, \"clone\", function clone(overrides: Overrides = {}) {\n      const { line, column, index } = overrides.loc ?? loc;\n      return constructor(new Position(line, column, index), {\n        ...details,\n        ...overrides.details,\n      });\n    });\n\n    defineHidden(error, \"details\", details);\n\n    Object.defineProperty(error, \"message\", {\n      configurable: true,\n      get(this: ParseError<ErrorDetails>): string {\n        const message = `${toMessage(details)} (${loc.line}:${loc.column})`;\n        this.message = message;\n        return message;\n      },\n      set(value: string) {\n        Object.defineProperty(this, \"message\", { value, writable: true });\n      },\n    });\n\n    return error;\n  };\n}\n\ntype ParseErrorTemplate =\n  | string\n  | ToMessage<any>\n  | { message: string | ToMessage<any>; code?: ParseErrorCode };\n\nexport type ParseErrorTemplates = { [reasonCode: string]: ParseErrorTemplate };\n\n// This is the templated form of `ParseErrorEnum`.\n//\n// Note: We could factor out the return type calculation into something like\n// `ParseErrorConstructor<T extends ParseErrorTemplates>`, and then we could\n// reuse it in the non-templated form of `ParseErrorEnum`, but TypeScript\n// doesn't seem to drill down that far when showing you the computed type of\n// an object in an editor, so we'll leave it inlined for now.\nexport function ParseErrorEnum(a: TemplateStringsArray): <\n  T extends ParseErrorTemplates,\n>(\n  parseErrorTemplates: T,\n) => {\n  [K in keyof T]: ParseErrorConstructor<\n    T[K] extends { message: string | ToMessage<any> }\n      ? T[K][\"message\"] extends ToMessage<any>\n        ? Parameters<T[K][\"message\"]>[0]\n        : object\n      : T[K] extends ToMessage<any>\n        ? Parameters<T[K]>[0]\n        : object\n  >;\n};\n\nexport function ParseErrorEnum<T extends ParseErrorTemplates>(\n  parseErrorTemplates: T,\n  syntaxPlugin?: SyntaxPlugin,\n): {\n  [K in keyof T]: ParseErrorConstructor<\n    T[K] extends { message: string | ToMessage<any> }\n      ? T[K][\"message\"] extends ToMessage<any>\n        ? Parameters<T[K][\"message\"]>[0]\n        : object\n      : T[K] extends ToMessage<any>\n        ? Parameters<T[K]>[0]\n        : object\n  >;\n};\n\n// You call `ParseErrorEnum` with a mapping from `ReasonCode`'s to either:\n//\n// 1. a static error message,\n// 2. `toMessage` functions that define additional necessary `details` needed by\n//    the `ParseError`, or\n// 3. Objects that contain a `message` of one of the above and overridden `code`\n//    and/or `reasonCode`:\n//\n// ParseErrorEnum `optionalSyntaxPlugin` ({\n//   ErrorWithStaticMessage: \"message\",\n//   ErrorWithDynamicMessage: ({ type } : { type: string }) => `${type}`),\n//   ErrorWithOverriddenCodeAndOrReasonCode: {\n//     message: ({ type }: { type: string }) => `${type}`),\n//     code: \"AN_ERROR_CODE\",\n//     ...(BABEL_8_BREAKING ? { } : { reasonCode: \"CustomErrorReasonCode\" })\n//   }\n// });\n//\nexport function ParseErrorEnum(\n  argument: TemplateStringsArray | ParseErrorTemplates,\n  syntaxPlugin?: SyntaxPlugin,\n) {\n  // If the first parameter is an array, that means we were called with a tagged\n  // template literal. Extract the syntaxPlugin from this, and call again in\n  // the \"normalized\" form.\n  if (Array.isArray(argument)) {\n    return (parseErrorTemplates: ParseErrorTemplates) =>\n      ParseErrorEnum(parseErrorTemplates, argument[0]);\n  }\n\n  const ParseErrorConstructors = {} as Record<\n    string,\n    ParseErrorConstructor<unknown>\n  >;\n\n  for (const reasonCode of Object.keys(argument)) {\n    const template = (argument as ParseErrorTemplates)[reasonCode];\n    const { message, ...rest } =\n      typeof template === \"string\"\n        ? { message: () => template }\n        : typeof template === \"function\"\n          ? { message: template }\n          : template;\n    const toMessage = typeof message === \"string\" ? () => message : message;\n\n    ParseErrorConstructors[reasonCode] = toParseErrorConstructor({\n      code: \"BABEL_PARSER_SYNTAX_ERROR\",\n      reasonCode,\n      toMessage,\n      ...(syntaxPlugin ? { syntaxPlugin } : {}),\n      ...rest,\n    });\n  }\n\n  return ParseErrorConstructors;\n}\n\nimport ModuleErrors from \"./parse-error/module-errors.ts\";\nimport StandardErrors from \"./parse-error/standard-errors.ts\";\nimport StrictModeErrors from \"./parse-error/strict-mode-errors.ts\";\nimport ParseExpressionErrors from \"./parse-error/parse-expression-errors.ts\";\nimport PipelineOperatorErrors from \"./parse-error/pipeline-operator-errors.ts\";\n\nexport const Errors = {\n  ...ParseErrorEnum(ModuleErrors),\n  ...ParseErrorEnum(StandardErrors),\n  ...ParseErrorEnum(StrictModeErrors),\n  ...ParseErrorEnum(ParseExpressionErrors),\n  ...ParseErrorEnum`pipelineOperator`(PipelineOperatorErrors),\n};\n\nexport type { LValAncestor } from \"./parse-error/standard-errors.ts\";\n","import type { Plugin } from \"./plugin-utils.ts\";\n\n// A second optional argument can be given to further configure\n// the parser process. These options are recognized:\n\nexport type SourceType = \"script\" | \"module\" | \"unambiguous\";\n\nexport interface Options {\n  /**\n   * By default, import and export declarations can only appear at a program's top level.\n   * Setting this option to true allows them anywhere where a statement is allowed.\n   */\n  allowImportExportEverywhere?: boolean;\n\n  /**\n   * By default, await use is not allowed outside of an async function.\n   * Set this to true to accept such code.\n   */\n  allowAwaitOutsideFunction?: boolean;\n\n  /**\n   * By default, a return statement at the top level raises an error.\n   * Set this to true to accept such code.\n   */\n  allowReturnOutsideFunction?: boolean;\n\n  /**\n   * By default, new.target use is not allowed outside of a function or class.\n   * Set this to true to accept such code.\n   */\n  allowNewTargetOutsideFunction?: boolean;\n\n  allowSuperOutsideMethod?: boolean;\n\n  /**\n   * By default, exported identifiers must refer to a declared variable.\n   * Set this to true to allow export statements to reference undeclared variables.\n   */\n  allowUndeclaredExports?: boolean;\n\n  /**\n   * By default, yield use is not allowed outside of a generator function.\n   * Set this to true to accept such code.\n   */\n\n  allowYieldOutsideFunction?: boolean;\n\n  /**\n   * By default, Babel parser JavaScript code according to Annex B syntax.\n   * Set this to `false` to disable such behavior.\n   */\n  annexB?: boolean;\n\n  /**\n   * By default, Babel attaches comments to adjacent AST nodes.\n   * When this option is set to false, comments are not attached.\n   * It can provide up to 30% performance improvement when the input code has many comments.\n   * @babel/eslint-parser will set it for you.\n   * It is not recommended to use attachComment: false with Babel transform,\n   * as doing so removes all the comments in output code, and renders annotations such as\n   * /* istanbul ignore next *\\/ nonfunctional.\n   */\n  attachComment?: boolean;\n\n  /**\n   * By default, Babel always throws an error when it finds some invalid code.\n   * When this option is set to true, it will store the parsing error and\n   * try to continue parsing the invalid input file.\n   */\n  errorRecovery?: boolean;\n\n  /**\n   * Indicate the mode the code should be parsed in.\n   * Can be one of \"script\", \"module\", or \"unambiguous\". Defaults to \"script\".\n   * \"unambiguous\" will make @babel/parser attempt to guess, based on the presence\n   * of ES6 import or export statements.\n   * Files with ES6 imports and exports are considered \"module\" and are otherwise \"script\".\n   */\n  sourceType?: \"script\" | \"module\" | \"unambiguous\";\n\n  /**\n   * Correlate output AST nodes with their source filename.\n   * Useful when generating code and source maps from the ASTs of multiple input files.\n   */\n  sourceFilename?: string;\n\n  /**\n   * By default, all source indexes start from 0.\n   * You can provide a start index to alternatively start with.\n   * Useful for integration with other source tools.\n   */\n  startIndex?: number;\n\n  /**\n   * By default, the first line of code parsed is treated as line 1.\n   * You can provide a line number to alternatively start with.\n   * Useful for integration with other source tools.\n   */\n  startLine?: number;\n\n  /**\n   * By default, the parsed code is treated as if it starts from line 1, column 0.\n   * You can provide a column number to alternatively start with.\n   * Useful for integration with other source tools.\n   */\n  startColumn?: number;\n\n  /**\n   * Array containing the plugins that you want to enable.\n   */\n  plugins?: Plugin[];\n\n  /**\n   * Should the parser work in strict mode.\n   * Defaults to true if sourceType === 'module'. Otherwise, false.\n   */\n  strictMode?: boolean;\n\n  /**\n   * Adds a ranges property to each node: [node.start, node.end]\n   */\n  ranges?: boolean;\n\n  /**\n   * Adds all parsed tokens to a tokens property on the File node.\n   */\n  tokens?: boolean;\n\n  /**\n   * By default, the parser adds information about parentheses by setting\n   * `extra.parenthesized` to `true` as needed.\n   * When this option is `true` the parser creates `ParenthesizedExpression`\n   * AST nodes instead of using the `extra` property.\n   */\n  createParenthesizedExpressions?: boolean;\n\n  /**\n   * The default is false in Babel 7 and true in Babel 8\n   * Set this to true to parse it as an `ImportExpression` node.\n   * Otherwise `import(foo)` is parsed as `CallExpression(Import, [Identifier(foo)])`.\n   */\n  createImportExpressions?: boolean;\n}\n\nexport const enum OptionFlags {\n  AllowAwaitOutsideFunction = 1 << 0,\n  AllowReturnOutsideFunction = 1 << 1,\n  AllowNewTargetOutsideFunction = 1 << 2,\n  AllowImportExportEverywhere = 1 << 3,\n  AllowSuperOutsideMethod = 1 << 4,\n  AllowYieldOutsideFunction = 1 << 5,\n  AllowUndeclaredExports = 1 << 6,\n  Ranges = 1 << 7,\n  Tokens = 1 << 8,\n  CreateImportExpressions = 1 << 9,\n  CreateParenthesizedExpressions = 1 << 10,\n  ErrorRecovery = 1 << 11,\n  AttachComment = 1 << 12,\n  AnnexB = 1 << 13,\n}\n\ntype OptionsWithDefaults = Required<Options>;\n\nfunction createDefaultOptions(): OptionsWithDefaults {\n  return {\n    // Source type (\"script\" or \"module\") for different semantics\n    sourceType: \"script\",\n    // Source filename.\n    sourceFilename: undefined,\n    // Index (0-based) from which to start counting source. Useful for\n    // integration with other tools.\n    startIndex: 0,\n    // Column (0-based) from which to start counting source. Useful for\n    // integration with other tools.\n    startColumn: 0,\n    // Line (1-based) from which to start counting source. Useful for\n    // integration with other tools.\n    startLine: 1,\n    // When enabled, await at the top level is not considered an\n    // error.\n    allowAwaitOutsideFunction: false,\n    // When enabled, a return at the top level is not considered an\n    // error.\n    allowReturnOutsideFunction: false,\n    // When enabled, new.target outside a function or class is not\n    // considered an error.\n    allowNewTargetOutsideFunction: false,\n    // When enabled, import/export statements are not constrained to\n    // appearing at the top of the program.\n    allowImportExportEverywhere: false,\n    // TODO\n    allowSuperOutsideMethod: false,\n    // When enabled, export statements can reference undeclared variables.\n    allowUndeclaredExports: false,\n    allowYieldOutsideFunction: false,\n    // An array of plugins to enable\n    plugins: [],\n    // TODO\n    strictMode: null,\n    // Nodes have their start and end characters offsets recorded in\n    // `start` and `end` properties (directly on the node, rather than\n    // the `loc` object, which holds line/column data. To also add a\n    // [semi-standardized][range] `range` property holding a `[start,\n    // end]` array with the same numbers, set the `ranges` option to\n    // `true`.\n    //\n    // [range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n    ranges: false,\n    // Adds all parsed tokens to a `tokens` property on the `File` node\n    tokens: false,\n    // Whether to create ImportExpression AST nodes (if false\n    // `import(foo)` will be parsed as CallExpression(Import, [Identifier(foo)])\n    createImportExpressions: process.env.BABEL_8_BREAKING ? true : false,\n    // Whether to create ParenthesizedExpression AST nodes (if false\n    // the parser sets extra.parenthesized on the expression nodes instead).\n    createParenthesizedExpressions: false,\n    // When enabled, errors are attached to the AST instead of being directly thrown.\n    // Some errors will still throw, because @babel/parser can't always recover.\n    errorRecovery: false,\n    // When enabled, comments will be attached to adjacent AST nodes as one of\n    // `leadingComments`, `trailingComments` and `innerComments`. The comment attachment\n    // is vital to preserve comments after transform. If you don't print AST back,\n    // consider set this option to `false` for performance\n    attachComment: true,\n    // When enabled, the parser will support Annex B syntax.\n    // https://tc39.es/ecma262/#sec-additional-ecmascript-features-for-web-browsers\n    annexB: true,\n  };\n}\n\n// Interpret and default an options object\n\nexport function getOptions(opts?: Options | null): OptionsWithDefaults {\n  // https://github.com/babel/babel/pull/16918\n  // `options` is accessed frequently, please make sure it is a fast object.\n  // `%ToFastProperties` can make it a fast object, but the performance is the same as the slow object.\n  const options: any = createDefaultOptions();\n\n  if (opts == null) {\n    return options;\n  }\n  if (opts.annexB != null && opts.annexB !== false) {\n    throw new Error(\"The `annexB` option can only be set to `false`.\");\n  }\n\n  for (const key of Object.keys(options) as (keyof Options)[]) {\n    if (opts[key] != null) options[key] = opts[key];\n  }\n\n  if (options.startLine === 1) {\n    if (opts.startIndex == null && options.startColumn > 0) {\n      options.startIndex = options.startColumn;\n    } else if (opts.startColumn == null && options.startIndex > 0) {\n      options.startColumn = options.startIndex;\n    }\n  } else if (opts.startColumn == null || opts.startIndex == null) {\n    if (opts.startIndex != null || process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"With a `startLine > 1` you must also specify `startIndex` and `startColumn`.\",\n      );\n    }\n  }\n\n  return options;\n}\n","import type { TokenType } from \"../tokenizer/types.ts\";\nimport type Parser from \"../parser/index.ts\";\nimport type * as N from \"../types.ts\";\nimport type { Node as NodeType, NodeBase, File } from \"../types.ts\";\nimport type { Position } from \"../util/location.ts\";\nimport { Errors } from \"../parse-error.ts\";\nimport type { Undone } from \"../parser/node.ts\";\nimport type { BindingFlag } from \"../util/scopeflags.ts\";\nimport { OptionFlags } from \"../options.ts\";\nimport type { ExpressionErrors } from \"../parser/util.ts\";\n\nconst { defineProperty } = Object;\nconst toUnenumerable = (object: any, key: string) => {\n  if (object) {\n    defineProperty(object, key, { enumerable: false, value: object[key] });\n  }\n};\n\nfunction toESTreeLocation(node: any) {\n  toUnenumerable(node.loc.start, \"index\");\n  toUnenumerable(node.loc.end, \"index\");\n\n  return node;\n}\n\nexport default (superClass: typeof Parser) =>\n  class ESTreeParserMixin extends superClass implements Parser {\n    parse(): File {\n      const file = toESTreeLocation(super.parse());\n\n      if (this.optionFlags & OptionFlags.Tokens) {\n        file.tokens = file.tokens.map(toESTreeLocation);\n      }\n\n      return file;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseRegExpLiteral({ pattern, flags }): N.EstreeRegExpLiteral {\n      let regex: RegExp | null = null;\n      try {\n        regex = new RegExp(pattern, flags);\n      } catch (_) {\n        // In environments that don't support these flags value will\n        // be null as the regex can't be represented natively.\n      }\n      const node = this.estreeParseLiteral<N.EstreeRegExpLiteral>(regex);\n      node.regex = { pattern, flags };\n\n      return node;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseBigIntLiteral(value: any): N.Node {\n      // https://github.com/estree/estree/blob/master/es2020.md#bigintliteral\n      let bigInt: bigint | null;\n      try {\n        bigInt = BigInt(value);\n      } catch {\n        bigInt = null;\n      }\n      const node = this.estreeParseLiteral<N.EstreeBigIntLiteral>(bigInt);\n      node.bigint = String(node.value || value);\n\n      return node;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseDecimalLiteral(value: any): N.Node {\n      // https://github.com/estree/estree/blob/master/experimental/decimal.md\n      // todo: use BigDecimal when node supports it.\n      const decimal: null = null;\n      const node = this.estreeParseLiteral(decimal);\n      node.decimal = String(node.value || value);\n\n      return node;\n    }\n\n    estreeParseLiteral<T extends N.EstreeLiteral>(value: any) {\n      // @ts-expect-error ESTree plugin changes node types\n      return this.parseLiteral<T>(value, \"Literal\");\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseStringLiteral(value: any): N.Node {\n      return this.estreeParseLiteral(value);\n    }\n\n    parseNumericLiteral(value: any): any {\n      return this.estreeParseLiteral(value);\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    parseNullLiteral(): N.Node {\n      return this.estreeParseLiteral(null);\n    }\n\n    parseBooleanLiteral(value: boolean): N.BooleanLiteral {\n      // @ts-expect-error ESTree plugin changes node types\n      return this.estreeParseLiteral(value);\n    }\n\n    // https://github.com/estree/estree/blob/master/es2020.md#chainexpression\n    estreeParseChainExpression(\n      node: N.Expression,\n      endLoc: Position,\n    ): N.EstreeChainExpression {\n      const chain = this.startNodeAtNode<N.EstreeChainExpression>(node);\n      chain.expression = node;\n      return this.finishNodeAt(chain, \"ChainExpression\", endLoc);\n    }\n\n    // Cast a Directive to an ExpressionStatement. Mutates the input Directive.\n    directiveToStmt(directive: N.Directive): N.ExpressionStatement {\n      const expression = directive.value as any as N.EstreeLiteral;\n      delete directive.value;\n\n      this.castNodeTo(expression, \"Literal\");\n      expression.raw = expression.extra.raw;\n      expression.value = expression.extra.expressionValue;\n\n      const stmt = this.castNodeTo(directive, \"ExpressionStatement\");\n      stmt.expression = expression;\n      stmt.directive = expression.extra.rawValue;\n\n      delete expression.extra;\n\n      return stmt;\n    }\n\n    /**\n     * The TS-ESLint always define optional AST properties, here we provide the\n     * default value for such properties immediately after `finishNode` was invoked.\n     * This hook will be implemented by the typescript plugin.\n     *\n     * Note: This hook should be manually invoked when we change the `type` of a given AST\n     * node, to ensure that the optional properties are correctly filled.\n     * @param node The AST node finished by finishNode\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    fillOptionalPropertiesForTSESLint(node: NodeType) {}\n\n    cloneEstreeStringLiteral(node: N.EstreeLiteral): N.EstreeLiteral {\n      const { start, end, loc, range, raw, value } = node;\n      const cloned = Object.create(node.constructor.prototype);\n      cloned.type = \"Literal\";\n      cloned.start = start;\n      cloned.end = end;\n      cloned.loc = loc;\n      cloned.range = range;\n      cloned.raw = raw;\n      cloned.value = value;\n      return cloned;\n    }\n\n    // ==================================\n    // Overrides\n    // ==================================\n\n    initFunction(node: N.BodilessFunctionOrMethodBase, isAsync: boolean): void {\n      super.initFunction(node, isAsync);\n      node.expression = false;\n    }\n\n    checkDeclaration(node: N.Pattern | N.ObjectProperty): void {\n      if (node != null && this.isObjectProperty(node)) {\n        // @ts-expect-error plugin typings\n        this.checkDeclaration((node as unknown as N.EstreeProperty).value);\n      } else {\n        super.checkDeclaration(node);\n      }\n    }\n\n    getObjectOrClassMethodParams(method: N.ObjectMethod | N.ClassMethod) {\n      return (method as unknown as N.EstreeMethodDefinition).value.params;\n    }\n\n    isValidDirective(stmt: N.Statement): stmt is N.ExpressionStatement {\n      return (\n        stmt.type === \"ExpressionStatement\" &&\n        stmt.expression.type === \"Literal\" &&\n        typeof stmt.expression.value === \"string\" &&\n        !stmt.expression.extra?.parenthesized\n      );\n    }\n\n    parseBlockBody(\n      node: N.BlockStatementLike,\n      allowDirectives: boolean | undefined | null,\n      topLevel: boolean,\n      end: TokenType,\n      afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n    ): void {\n      super.parseBlockBody(\n        node,\n        allowDirectives,\n        topLevel,\n        end,\n        afterBlockParse,\n      );\n\n      const directiveStatements = node.directives.map(d =>\n        this.directiveToStmt(d),\n      );\n      // @ts-expect-error estree plugin typings\n      node.body = directiveStatements.concat(node.body);\n      delete node.directives;\n    }\n\n    parsePrivateName(): any {\n      const node = super.parsePrivateName();\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return node;\n        }\n      }\n      return this.convertPrivateNameToPrivateIdentifier(node);\n    }\n\n    convertPrivateNameToPrivateIdentifier(\n      node: N.PrivateName,\n    ): N.EstreePrivateIdentifier {\n      const name = super.getPrivateNameSV(node);\n      node = node as any;\n      delete node.id;\n      // @ts-expect-error mutate AST types\n      node.name = name;\n      return this.castNodeTo(node, \"PrivateIdentifier\");\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    isPrivateName(node: N.Node): node is N.EstreePrivateIdentifier {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return super.isPrivateName(node);\n        }\n      }\n      return node.type === \"PrivateIdentifier\";\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    getPrivateNameSV(node: N.EstreePrivateIdentifier): string {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return super.getPrivateNameSV(node as unknown as N.PrivateName);\n        }\n      }\n      return node.name;\n    }\n\n    // @ts-expect-error plugin may override interfaces\n    parseLiteral<T extends N.Literal>(value: any, type: T[\"type\"]): T {\n      const node = super.parseLiteral<T>(value, type);\n      // @ts-expect-error mutating AST types\n      node.raw = node.extra.raw;\n      delete node.extra;\n\n      return node;\n    }\n\n    parseFunctionBody(\n      node: N.Function,\n      allowExpression?: boolean | null,\n      isMethod: boolean = false,\n    ): void {\n      super.parseFunctionBody(node, allowExpression, isMethod);\n      node.expression = node.body.type !== \"BlockStatement\";\n    }\n\n    // @ts-expect-error plugin may override interfaces\n    parseMethod<\n      T extends N.ClassPrivateMethod | N.ObjectMethod | N.ClassMethod,\n    >(\n      node: Undone<T>,\n      isGenerator: boolean,\n      isAsync: boolean,\n      isConstructor: boolean,\n      allowDirectSuper: boolean,\n      type: T[\"type\"],\n      inClassScope: boolean = false,\n    ):\n      | N.EstreeProperty\n      | N.EstreeMethodDefinition\n      | N.EstreeTSAbstractMethodDefinition {\n      let funcNode = this.startNode<N.MethodLike>();\n      funcNode.kind = node.kind; // provide kind, so super method correctly sets state\n      funcNode = super.parseMethod(\n        funcNode,\n        isGenerator,\n        isAsync,\n        isConstructor,\n        allowDirectSuper,\n        type,\n        inClassScope,\n      );\n      delete funcNode.kind;\n      const { typeParameters } = node;\n      if (typeParameters) {\n        delete node.typeParameters;\n        funcNode.typeParameters = typeParameters;\n        this.resetStartLocationFromNode(funcNode, typeParameters);\n      }\n      const valueNode = this.castNodeTo(\n        funcNode as N.MethodLike,\n        process.env.BABEL_8_BREAKING &&\n          this.hasPlugin(\"typescript\") &&\n          !funcNode.body\n          ? \"TSEmptyBodyFunctionExpression\"\n          : \"FunctionExpression\",\n      );\n      (\n        node as unknown as Undone<\n          | N.EstreeProperty\n          | N.EstreeMethodDefinition\n          | N.EstreeTSAbstractMethodDefinition\n        >\n      ).value = valueNode;\n      if (type === \"ClassPrivateMethod\") {\n        node.computed = false;\n      }\n      if (process.env.BABEL_8_BREAKING && this.hasPlugin(\"typescript\")) {\n        // @ts-expect-error todo(flow->ts) property not defined for all types in union\n        if (node.abstract) {\n          // @ts-expect-error remove abstract from TSAbstractMethodDefinition\n          delete node.abstract;\n          return this.finishNode(\n            // @ts-expect-error cast methods to estree types\n            node as Undone<N.EstreeTSAbstractMethodDefinition>,\n            \"TSAbstractMethodDefinition\",\n          );\n        }\n      }\n      if (type === \"ObjectMethod\") {\n        if ((node as any as N.ObjectMethod).kind === \"method\") {\n          (node as any as N.EstreeProperty).kind = \"init\";\n        }\n        (node as any as N.EstreeProperty).shorthand = false;\n        return this.finishNode(\n          // @ts-expect-error cast methods to estree types\n          node as Undone<N.EstreeProperty>,\n          \"Property\",\n        );\n      } else {\n        return this.finishNode(\n          // @ts-expect-error cast methods to estree types\n          node as Undone<N.EstreeMethodDefinition>,\n          \"MethodDefinition\",\n        );\n      }\n    }\n\n    nameIsConstructor(key: N.Expression | N.PrivateName): boolean {\n      if (key.type === \"Literal\") return key.value === \"constructor\";\n      return super.nameIsConstructor(key);\n    }\n\n    parseClassProperty(...args: [N.ClassProperty]): any {\n      const propertyNode = super.parseClassProperty(...args);\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return propertyNode as unknown as N.EstreePropertyDefinition;\n        }\n      }\n      if (\n        process.env.BABEL_8_BREAKING &&\n        propertyNode.abstract &&\n        this.hasPlugin(\"typescript\")\n      ) {\n        delete propertyNode.abstract;\n        this.castNodeTo(propertyNode, \"TSAbstractPropertyDefinition\");\n      } else {\n        this.castNodeTo(propertyNode, \"PropertyDefinition\");\n      }\n      return propertyNode;\n    }\n\n    parseClassPrivateProperty(...args: [N.ClassPrivateProperty]): any {\n      const propertyNode = super.parseClassPrivateProperty(...args);\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return propertyNode as unknown as N.EstreePropertyDefinition;\n        }\n      }\n      if (\n        process.env.BABEL_8_BREAKING &&\n        propertyNode.abstract &&\n        this.hasPlugin(\"typescript\")\n      ) {\n        this.castNodeTo(propertyNode, \"TSAbstractPropertyDefinition\");\n      } else {\n        this.castNodeTo(propertyNode, \"PropertyDefinition\");\n      }\n      propertyNode.computed = false;\n      return propertyNode;\n    }\n\n    parseClassAccessorProperty(\n      this: Parser,\n      node: N.ClassAccessorProperty,\n    ): any {\n      const accessorPropertyNode = super.parseClassAccessorProperty(node);\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!this.getPluginOption(\"estree\", \"classFeatures\")) {\n          return accessorPropertyNode;\n        }\n      }\n      if (accessorPropertyNode.abstract && this.hasPlugin(\"typescript\")) {\n        delete accessorPropertyNode.abstract;\n        this.castNodeTo(accessorPropertyNode, \"TSAbstractAccessorProperty\");\n      } else {\n        this.castNodeTo(accessorPropertyNode, \"AccessorProperty\");\n      }\n      return accessorPropertyNode;\n    }\n\n    parseObjectProperty(\n      prop: N.ObjectProperty,\n      startLoc: Position | undefined | null,\n      isPattern: boolean,\n      refExpressionErrors?: ExpressionErrors | null,\n    ): N.ObjectProperty | undefined | null {\n      const node: N.EstreeProperty = super.parseObjectProperty(\n        prop,\n        startLoc,\n        isPattern,\n        refExpressionErrors,\n      ) as any;\n\n      if (node) {\n        node.kind = \"init\";\n        this.castNodeTo(node, \"Property\");\n      }\n\n      return node as any;\n    }\n\n    finishObjectProperty(node: Undone<N.ObjectProperty>): N.ObjectProperty {\n      (node as unknown as Undone<N.EstreeProperty>).kind = \"init\";\n      return this.finishNode(\n        node as unknown as Undone<N.EstreeProperty>,\n        \"Property\",\n      ) as any;\n    }\n\n    isValidLVal(\n      type: string,\n      isUnparenthesizedInAssign: boolean,\n      binding: BindingFlag,\n    ) {\n      return type === \"Property\"\n        ? \"value\"\n        : super.isValidLVal(type, isUnparenthesizedInAssign, binding);\n    }\n\n    isAssignable(node: N.Node, isBinding?: boolean): boolean {\n      if (node != null && this.isObjectProperty(node)) {\n        return this.isAssignable(node.value, isBinding);\n      }\n      return super.isAssignable(node, isBinding);\n    }\n\n    toAssignable(node: N.Node, isLHS: boolean = false): void {\n      if (node != null && this.isObjectProperty(node)) {\n        const { key, value } = node;\n        if (this.isPrivateName(key)) {\n          this.classScope.usePrivateName(\n            this.getPrivateNameSV(key),\n            key.loc.start,\n          );\n        }\n        this.toAssignable(value, isLHS);\n      } else {\n        super.toAssignable(node, isLHS);\n      }\n    }\n\n    toAssignableObjectExpressionProp(\n      prop: N.Node,\n      isLast: boolean,\n      isLHS: boolean,\n    ) {\n      if (\n        prop.type === \"Property\" &&\n        (prop.kind === \"get\" || prop.kind === \"set\")\n      ) {\n        this.raise(Errors.PatternHasAccessor, prop.key);\n      } else if (prop.type === \"Property\" && prop.method) {\n        this.raise(Errors.PatternHasMethod, prop.key);\n      } else {\n        super.toAssignableObjectExpressionProp(prop, isLast, isLHS);\n      }\n    }\n\n    finishCallExpression<T extends N.CallExpression | N.OptionalCallExpression>(\n      unfinished: Undone<T>,\n      optional: boolean,\n    ): T {\n      const node = super.finishCallExpression(unfinished, optional);\n\n      if (node.callee.type === \"Import\") {\n        this.castNodeTo(node, \"ImportExpression\");\n        (node as N.Node as N.EstreeImportExpression).source = node\n          .arguments[0] as N.Expression;\n        (node as N.Node as N.EstreeImportExpression).options =\n          (node.arguments[1] as N.Expression) ?? null;\n        // compatibility with previous ESTree AST\n        // TODO(Babel 8): Remove this\n        (node as N.Node as N.EstreeImportExpression).attributes =\n          (node.arguments[1] as N.Expression) ?? null;\n        // arguments isn't optional in the type definition\n        delete node.arguments;\n        // callee isn't optional in the type definition\n        delete node.callee;\n      } else if (node.type === \"OptionalCallExpression\") {\n        this.castNodeTo(node, \"CallExpression\");\n      } else {\n        node.optional = false;\n      }\n\n      return node;\n    }\n\n    toReferencedArguments(\n      node:\n        | N.CallExpression\n        | N.OptionalCallExpression\n        | N.EstreeImportExpression,\n      /* isParenthesizedExpr?: boolean, */\n    ) {\n      // ImportExpressions do not have an arguments array.\n      if (node.type === \"ImportExpression\") {\n        return;\n      }\n\n      super.toReferencedArguments(node);\n    }\n\n    parseExport(\n      unfinished: Undone<N.AnyExport>,\n      decorators: N.Decorator[] | null,\n    ) {\n      const exportStartLoc = this.state.lastTokStartLoc;\n      const node = super.parseExport(unfinished, decorators);\n\n      switch (node.type) {\n        case \"ExportAllDeclaration\":\n          // @ts-expect-error mutating AST types\n          node.exported = null;\n          break;\n\n        case \"ExportNamedDeclaration\":\n          if (\n            node.specifiers.length === 1 &&\n            node.specifiers[0].type === \"ExportNamespaceSpecifier\"\n          ) {\n            this.castNodeTo(node, \"ExportAllDeclaration\");\n            // @ts-expect-error mutating AST types\n            node.exported = node.specifiers[0].exported;\n            delete node.specifiers;\n          }\n\n        // fallthrough\n        case \"ExportDefaultDeclaration\":\n          {\n            const { declaration } = node;\n            if (\n              declaration?.type === \"ClassDeclaration\" &&\n              declaration.decorators?.length > 0 &&\n              // decorator comes before export\n              declaration.start === node.start\n            ) {\n              this.resetStartLocation(\n                node,\n                // For compatibility with ESLint's keyword-spacing rule, which assumes that an\n                // export declaration must start with export.\n                // https://github.com/babel/babel/issues/15085\n                // Here we reset export declaration's start to be the start of the export token\n                exportStartLoc,\n              );\n            }\n          }\n\n          break;\n      }\n\n      return node;\n    }\n\n    stopParseSubscript(base: N.Expression, state: N.ParseSubscriptState) {\n      const node = super.stopParseSubscript(base, state);\n      if (state.optionalChainMember) {\n        return this.estreeParseChainExpression(node, base.loc.end);\n      }\n      return node;\n    }\n\n    parseMember(\n      base: N.Expression,\n      startLoc: Position,\n      state: N.ParseSubscriptState,\n      computed: boolean,\n      optional: boolean,\n    ) {\n      const node = super.parseMember(base, startLoc, state, computed, optional);\n      if (node.type === \"OptionalMemberExpression\") {\n        this.castNodeTo(node, \"MemberExpression\");\n      } else {\n        node.optional = false;\n      }\n      return node;\n    }\n\n    isOptionalMemberExpression(node: N.Node) {\n      if (node.type === \"ChainExpression\") {\n        return node.expression.type === \"MemberExpression\";\n      }\n      return super.isOptionalMemberExpression(node);\n    }\n\n    hasPropertyAsPrivateName(node: N.Node): boolean {\n      if (node.type === \"ChainExpression\") {\n        node = node.expression;\n      }\n      return super.hasPropertyAsPrivateName(node);\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    isObjectProperty(node: N.Node): node is N.EstreeProperty {\n      return node.type === \"Property\" && node.kind === \"init\" && !node.method;\n    }\n\n    // @ts-expect-error ESTree plugin changes node types\n    isObjectMethod(node: N.Node): node is N.EstreeProperty {\n      return (\n        node.type === \"Property\" &&\n        (node.method || node.kind === \"get\" || node.kind === \"set\")\n      );\n    }\n\n    /* ============================================================ *\n     * parser/node.ts                                               *\n     * ============================================================ */\n\n    castNodeTo<T extends N.Node[\"type\"]>(\n      node: N.Node,\n      type: T,\n    ): Extract<N.Node, { type: T }> {\n      const result = super.castNodeTo(node, type);\n      this.fillOptionalPropertiesForTSESLint(result);\n      return result;\n    }\n\n    cloneIdentifier<T extends N.Identifier | N.Placeholder>(node: T): T {\n      const cloned = super.cloneIdentifier(node);\n      this.fillOptionalPropertiesForTSESLint(cloned);\n      return cloned;\n    }\n\n    cloneStringLiteral<\n      T extends N.EstreeLiteral | N.StringLiteral | N.Placeholder,\n    >(node: T): T {\n      if (node.type === \"Literal\") {\n        return this.cloneEstreeStringLiteral(node) as T;\n      }\n      return super.cloneStringLiteral(node);\n    }\n\n    finishNodeAt<T extends NodeType>(\n      node: Undone<T>,\n      type: T[\"type\"],\n      endLoc: Position,\n    ): T {\n      return toESTreeLocation(super.finishNodeAt(node, type, endLoc));\n    }\n\n    // Override for TS-ESLint that does not allow optional AST properties\n    finishNode<T extends NodeType>(node: Undone<T>, type: T[\"type\"]): T {\n      const result = super.finishNode(node, type);\n      this.fillOptionalPropertiesForTSESLint(result);\n      return result;\n    }\n\n    resetStartLocation(node: N.Node, startLoc: Position) {\n      super.resetStartLocation(node, startLoc);\n      toESTreeLocation(node);\n    }\n\n    resetEndLocation(\n      node: NodeBase,\n      endLoc: Position = this.state.lastTokEndLoc,\n    ): void {\n      super.resetEndLocation(node, endLoc);\n      toESTreeLocation(node);\n    }\n  };\n","// The token context is used in JSX plugin to track\n// jsx tag / jsx text / normal JavaScript expression\n\nexport class TokContext {\n  constructor(token: string, preserveSpace?: boolean) {\n    this.token = token;\n    this.preserveSpace = !!preserveSpace;\n  }\n\n  token: string;\n  preserveSpace: boolean;\n}\n\nconst types: {\n  [key: string]: TokContext;\n} = {\n  brace: new TokContext(\"{\"), // normal JavaScript expression\n  j_oTag: new TokContext(\"<tag\"), // JSX opening tag\n  j_cTag: new TokContext(\"</tag\"), // JSX closing tag\n  j_expr: new TokContext(\"<tag>...</tag>\", true), // JSX expressions\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  types.template = new TokContext(\"`\", true);\n}\n\nexport { types };\n","import { types as tc, type TokContext } from \"./context.ts\";\n// ## Token types\n\n// The assignment of fine-grained, information-carrying type objects\n// allows the tokenizer to store the information it has about a\n// token in a way that is very cheap for the parser to look up.\n\n// All token type variables start with an underscore, to make them\n// easy to recognize.\n\n// The `beforeExpr` property is used to disambiguate between 1) binary\n// expression (<) and JSX Tag start (<name>); 2) object literal and JSX\n// texts. It is set on the `updateContext` function in the JSX plugin.\n\n// The `startsExpr` property is used to determine whether an expression\n// may be the “argument” subexpression of a `yield` expression or\n// `yield` statement. It is set on all token types that may be at the\n// start of a subexpression.\n\n// `isLoop` marks a keyword as starting a loop, which is important\n// to know when parsing a label, in order to allow or disallow\n// continue jumps to that label.\n\nconst beforeExpr = true;\nconst startsExpr = true;\nconst isLoop = true;\nconst isAssign = true;\nconst prefix = true;\nconst postfix = true;\n\ntype TokenOptions = {\n  keyword?: string;\n  beforeExpr?: boolean;\n  startsExpr?: boolean;\n  rightAssociative?: boolean;\n  isLoop?: boolean;\n  isAssign?: boolean;\n  prefix?: boolean;\n  postfix?: boolean;\n  binop?: number | null;\n};\n\n// Internally the tokenizer stores token as a number\nexport type TokenType = number;\n\n// The `ExportedTokenType` is exported via `tokTypes` and accessible\n// when `tokens: true` is enabled. Unlike internal token type, it provides\n// metadata of the tokens.\nexport class ExportedTokenType {\n  label: string;\n  keyword: string | undefined | null;\n  beforeExpr: boolean;\n  startsExpr: boolean;\n  rightAssociative: boolean;\n  isLoop: boolean;\n  isAssign: boolean;\n  prefix: boolean;\n  postfix: boolean;\n  binop: number | undefined | null;\n  // todo(Babel 8): remove updateContext from exposed token layout\n  declare updateContext:\n    | ((context: Array<TokContext>) => void)\n    | undefined\n    | null;\n\n  constructor(label: string, conf: TokenOptions = {}) {\n    this.label = label;\n    this.keyword = conf.keyword;\n    this.beforeExpr = !!conf.beforeExpr;\n    this.startsExpr = !!conf.startsExpr;\n    this.rightAssociative = !!conf.rightAssociative;\n    this.isLoop = !!conf.isLoop;\n    this.isAssign = !!conf.isAssign;\n    this.prefix = !!conf.prefix;\n    this.postfix = !!conf.postfix;\n    this.binop = conf.binop != null ? conf.binop : null;\n    if (!process.env.BABEL_8_BREAKING) {\n      this.updateContext = null;\n    }\n  }\n}\n\n// A map from keyword/keyword-like string value to the token type\nexport const keywords = new Map<string, TokenType>();\n\nfunction createKeyword(name: string, options: TokenOptions = {}): TokenType {\n  options.keyword = name;\n  const token = createToken(name, options);\n  keywords.set(name, token);\n  return token;\n}\n\nfunction createBinop(name: string, binop: number) {\n  return createToken(name, { beforeExpr, binop });\n}\n\nlet tokenTypeCounter = -1;\nexport const tokenTypes: ExportedTokenType[] = [];\nconst tokenLabels: string[] = [];\nconst tokenBinops: number[] = [];\nconst tokenBeforeExprs: boolean[] = [];\nconst tokenStartsExprs: boolean[] = [];\nconst tokenPrefixes: boolean[] = [];\n\nfunction createToken(name: string, options: TokenOptions = {}): TokenType {\n  ++tokenTypeCounter;\n  tokenLabels.push(name);\n  tokenBinops.push(options.binop ?? -1);\n  tokenBeforeExprs.push(options.beforeExpr ?? false);\n  tokenStartsExprs.push(options.startsExpr ?? false);\n  tokenPrefixes.push(options.prefix ?? false);\n  tokenTypes.push(new ExportedTokenType(name, options));\n\n  return tokenTypeCounter;\n}\n\nfunction createKeywordLike(\n  name: string,\n  options: TokenOptions = {},\n): TokenType {\n  ++tokenTypeCounter;\n  keywords.set(name, tokenTypeCounter);\n  tokenLabels.push(name);\n  tokenBinops.push(options.binop ?? -1);\n  tokenBeforeExprs.push(options.beforeExpr ?? false);\n  tokenStartsExprs.push(options.startsExpr ?? false);\n  tokenPrefixes.push(options.prefix ?? false);\n  // In the exported token type, we set the label as \"name\" for backward compatibility with Babel 7\n  tokenTypes.push(new ExportedTokenType(\"name\", options));\n\n  return tokenTypeCounter;\n}\n\n// For performance the token type helpers depend on the following declarations order.\n// When adding new token types, please also check if the token helpers need update.\n\nexport type InternalTokenTypes = typeof tt;\n\nexport const tt = {\n  // Punctuation token types.\n  bracketL: createToken(\"[\", { beforeExpr, startsExpr }),\n  // TODO: Remove this in Babel 8\n  bracketHashL: createToken(\"#[\", { beforeExpr, startsExpr }),\n  // TODO: Remove this in Babel 8\n  bracketBarL: createToken(\"[|\", { beforeExpr, startsExpr }),\n  bracketR: createToken(\"]\"),\n  // TODO: Remove this in Babel 8\n  bracketBarR: createToken(\"|]\"),\n  braceL: createToken(\"{\", { beforeExpr, startsExpr }),\n  // TODO: Remove this in Babel 8\n  braceBarL: createToken(\"{|\", { beforeExpr, startsExpr }),\n  // TODO: Remove this in Babel 8\n  braceHashL: createToken(\"#{\", { beforeExpr, startsExpr }),\n  braceR: createToken(\"}\"),\n  braceBarR: createToken(\"|}\"),\n  parenL: createToken(\"(\", { beforeExpr, startsExpr }),\n  parenR: createToken(\")\"),\n  comma: createToken(\",\", { beforeExpr }),\n  semi: createToken(\";\", { beforeExpr }),\n  colon: createToken(\":\", { beforeExpr }),\n  doubleColon: createToken(\"::\", { beforeExpr }),\n  dot: createToken(\".\"),\n  question: createToken(\"?\", { beforeExpr }),\n  questionDot: createToken(\"?.\"),\n  arrow: createToken(\"=>\", { beforeExpr }),\n  template: createToken(\"template\"),\n  ellipsis: createToken(\"...\", { beforeExpr }),\n  backQuote: createToken(\"`\", { startsExpr }),\n  dollarBraceL: createToken(\"${\", { beforeExpr, startsExpr }),\n  // start: isTemplate\n  templateTail: createToken(\"...`\", { startsExpr }),\n  templateNonTail: createToken(\"...${\", { beforeExpr, startsExpr }),\n  // end: isTemplate\n  at: createToken(\"@\"),\n  hash: createToken(\"#\", { startsExpr }),\n\n  // Special hashbang token.\n  interpreterDirective: createToken(\"#!...\"),\n\n  // Operators. These carry several kinds of properties to help the\n  // parser use them properly (the presence of these properties is\n  // what categorizes them as operators).\n  //\n  // `binop`, when present, specifies that this operator is a binary\n  // operator, and will refer to its precedence.\n  //\n  // `prefix` and `postfix` mark the operator as a prefix or postfix\n  // unary operator.\n  //\n  // `isAssign` marks all of `=`, `+=`, `-=` etcetera, which act as\n  // binary operators with a very low precedence, that should result\n  // in AssignmentExpression nodes.\n\n  // start: isAssign\n  eq: createToken(\"=\", { beforeExpr, isAssign }),\n  assign: createToken(\"_=\", { beforeExpr, isAssign }),\n  slashAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  // These are only needed to support % and ^ as a Hack-pipe topic token.\n  // When the proposal settles on a token, the others can be merged with\n  // tt.assign.\n  xorAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  moduloAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  // end: isAssign\n\n  incDec: createToken(\"++/--\", { prefix, postfix, startsExpr }),\n  bang: createToken(\"!\", { beforeExpr, prefix, startsExpr }),\n  tilde: createToken(\"~\", { beforeExpr, prefix, startsExpr }),\n\n  // More possible topic tokens.\n  // When the proposal settles on a token, at least one of these may be removed.\n  doubleCaret: createToken(\"^^\", { startsExpr }),\n  doubleAt: createToken(\"@@\", { startsExpr }),\n\n  // start: isBinop\n  pipeline: createBinop(\"|>\", 0),\n  nullishCoalescing: createBinop(\"??\", 1),\n  logicalOR: createBinop(\"||\", 1),\n  logicalAND: createBinop(\"&&\", 2),\n  bitwiseOR: createBinop(\"|\", 3),\n  bitwiseXOR: createBinop(\"^\", 4),\n  bitwiseAND: createBinop(\"&\", 5),\n  equality: createBinop(\"==/!=/===/!==\", 6),\n  lt: createBinop(\"</>/<=/>=\", 7),\n  gt: createBinop(\"</>/<=/>=\", 7),\n  relational: createBinop(\"</>/<=/>=\", 7),\n  bitShift: createBinop(\"<</>>/>>>\", 8),\n  bitShiftL: createBinop(\"<</>>/>>>\", 8),\n  bitShiftR: createBinop(\"<</>>/>>>\", 8),\n  plusMin: createToken(\"+/-\", { beforeExpr, binop: 9, prefix, startsExpr }),\n  // startsExpr: required by v8intrinsic plugin\n  modulo: createToken(\"%\", { binop: 10, startsExpr }),\n  // unset `beforeExpr` as it can be `function *`\n  star: createToken(\"*\", { binop: 10 }),\n  slash: createBinop(\"/\", 10),\n  exponent: createToken(\"**\", {\n    beforeExpr,\n    binop: 11,\n    rightAssociative: true,\n  }),\n\n  // Keywords\n  // Don't forget to update packages/babel-helper-validator-identifier/src/keyword.js\n  // when new keywords are added\n  // start: isLiteralPropertyName\n  // start: isKeyword\n  _in: createKeyword(\"in\", { beforeExpr, binop: 7 }),\n  _instanceof: createKeyword(\"instanceof\", { beforeExpr, binop: 7 }),\n  // end: isBinop\n  _break: createKeyword(\"break\"),\n  _case: createKeyword(\"case\", { beforeExpr }),\n  _catch: createKeyword(\"catch\"),\n  _continue: createKeyword(\"continue\"),\n  _debugger: createKeyword(\"debugger\"),\n  _default: createKeyword(\"default\", { beforeExpr }),\n  _else: createKeyword(\"else\", { beforeExpr }),\n  _finally: createKeyword(\"finally\"),\n  _function: createKeyword(\"function\", { startsExpr }),\n  _if: createKeyword(\"if\"),\n  _return: createKeyword(\"return\", { beforeExpr }),\n  _switch: createKeyword(\"switch\"),\n  _throw: createKeyword(\"throw\", { beforeExpr, prefix, startsExpr }),\n  _try: createKeyword(\"try\"),\n  _var: createKeyword(\"var\"),\n  _const: createKeyword(\"const\"),\n  _with: createKeyword(\"with\"),\n  _new: createKeyword(\"new\", { beforeExpr, startsExpr }),\n  _this: createKeyword(\"this\", { startsExpr }),\n  _super: createKeyword(\"super\", { startsExpr }),\n  _class: createKeyword(\"class\", { startsExpr }),\n  _extends: createKeyword(\"extends\", { beforeExpr }),\n  _export: createKeyword(\"export\"),\n  _import: createKeyword(\"import\", { startsExpr }),\n  _null: createKeyword(\"null\", { startsExpr }),\n  _true: createKeyword(\"true\", { startsExpr }),\n  _false: createKeyword(\"false\", { startsExpr }),\n  _typeof: createKeyword(\"typeof\", { beforeExpr, prefix, startsExpr }),\n  _void: createKeyword(\"void\", { beforeExpr, prefix, startsExpr }),\n  _delete: createKeyword(\"delete\", { beforeExpr, prefix, startsExpr }),\n  // start: isLoop\n  _do: createKeyword(\"do\", { isLoop, beforeExpr }),\n  _for: createKeyword(\"for\", { isLoop }),\n  _while: createKeyword(\"while\", { isLoop }),\n  // end: isLoop\n  // end: isKeyword\n\n  // Primary literals\n  // start: isIdentifier\n  _as: createKeywordLike(\"as\", { startsExpr }),\n  _assert: createKeywordLike(\"assert\", { startsExpr }),\n  _async: createKeywordLike(\"async\", { startsExpr }),\n  _await: createKeywordLike(\"await\", { startsExpr }),\n  _defer: createKeywordLike(\"defer\", { startsExpr }),\n  _from: createKeywordLike(\"from\", { startsExpr }),\n  _get: createKeywordLike(\"get\", { startsExpr }),\n  _let: createKeywordLike(\"let\", { startsExpr }),\n  _meta: createKeywordLike(\"meta\", { startsExpr }),\n  _of: createKeywordLike(\"of\", { startsExpr }),\n  _sent: createKeywordLike(\"sent\", { startsExpr }),\n  _set: createKeywordLike(\"set\", { startsExpr }),\n  _source: createKeywordLike(\"source\", { startsExpr }),\n  _static: createKeywordLike(\"static\", { startsExpr }),\n  _using: createKeywordLike(\"using\", { startsExpr }),\n  _yield: createKeywordLike(\"yield\", { startsExpr }),\n\n  // Flow and TypeScript Keywordlike\n  _asserts: createKeywordLike(\"asserts\", { startsExpr }),\n  _checks: createKeywordLike(\"checks\", { startsExpr }),\n  _exports: createKeywordLike(\"exports\", { startsExpr }),\n  _global: createKeywordLike(\"global\", { startsExpr }),\n  _implements: createKeywordLike(\"implements\", { startsExpr }),\n  _intrinsic: createKeywordLike(\"intrinsic\", { startsExpr }),\n  _infer: createKeywordLike(\"infer\", { startsExpr }),\n  _is: createKeywordLike(\"is\", { startsExpr }),\n  _mixins: createKeywordLike(\"mixins\", { startsExpr }),\n  _proto: createKeywordLike(\"proto\", { startsExpr }),\n  _require: createKeywordLike(\"require\", { startsExpr }),\n  _satisfies: createKeywordLike(\"satisfies\", { startsExpr }),\n  // start: isTSTypeOperator\n  _keyof: createKeywordLike(\"keyof\", { startsExpr }),\n  _readonly: createKeywordLike(\"readonly\", { startsExpr }),\n  _unique: createKeywordLike(\"unique\", { startsExpr }),\n  // end: isTSTypeOperator\n  // start: isTSDeclarationStart\n  _abstract: createKeywordLike(\"abstract\", { startsExpr }),\n  _declare: createKeywordLike(\"declare\", { startsExpr }),\n  _enum: createKeywordLike(\"enum\", { startsExpr }),\n  _module: createKeywordLike(\"module\", { startsExpr }),\n  _namespace: createKeywordLike(\"namespace\", { startsExpr }),\n  // start: isFlowInterfaceOrTypeOrOpaque\n  _interface: createKeywordLike(\"interface\", { startsExpr }),\n  _type: createKeywordLike(\"type\", { startsExpr }),\n  // end: isTSDeclarationStart\n  _opaque: createKeywordLike(\"opaque\", { startsExpr }),\n  // end: isFlowInterfaceOrTypeOrOpaque\n  name: createToken(\"name\", { startsExpr }),\n\n  // placeholder plugin\n  placeholder: createToken(\"%%\", { startsExpr: true }),\n  // end: isIdentifier\n\n  string: createToken(\"string\", { startsExpr }),\n  num: createToken(\"num\", { startsExpr }),\n  bigint: createToken(\"bigint\", { startsExpr }),\n  // TODO: Remove this in Babel 8\n  decimal: createToken(\"decimal\", { startsExpr }),\n  // end: isLiteralPropertyName\n  regexp: createToken(\"regexp\", { startsExpr }),\n  privateName: createToken(\"#name\", { startsExpr }),\n  eof: createToken(\"eof\"),\n\n  // jsx plugin\n  jsxName: createToken(\"jsxName\"),\n  jsxText: createToken(\"jsxText\", { beforeExpr: true }),\n  jsxTagStart: createToken(\"jsxTagStart\", { startsExpr: true }),\n  jsxTagEnd: createToken(\"jsxTagEnd\"),\n} as const;\n\nexport function tokenIsIdentifier(token: TokenType): boolean {\n  return token >= tt._as && token <= tt.placeholder;\n}\n\nexport function tokenKeywordOrIdentifierIsKeyword(token: TokenType): boolean {\n  // we can remove the token >= tt._in check when we\n  // know a token is either keyword or identifier\n  return token <= tt._while;\n}\n\nexport function tokenIsKeywordOrIdentifier(token: TokenType): boolean {\n  return token >= tt._in && token <= tt.placeholder;\n}\n\nexport function tokenIsLiteralPropertyName(token: TokenType): boolean {\n  return token >= tt._in && token <= tt.decimal;\n}\n\nexport function tokenComesBeforeExpression(token: TokenType): boolean {\n  return tokenBeforeExprs[token];\n}\n\nexport function tokenCanStartExpression(token: TokenType): boolean {\n  return tokenStartsExprs[token];\n}\n\nexport function tokenIsAssignment(token: TokenType): boolean {\n  return token >= tt.eq && token <= tt.moduloAssign;\n}\n\nexport function tokenIsFlowInterfaceOrTypeOrOpaque(token: TokenType): boolean {\n  return token >= tt._interface && token <= tt._opaque;\n}\n\nexport function tokenIsLoop(token: TokenType): boolean {\n  return token >= tt._do && token <= tt._while;\n}\n\nexport function tokenIsKeyword(token: TokenType): boolean {\n  return token >= tt._in && token <= tt._while;\n}\n\nexport function tokenIsOperator(token: TokenType): boolean {\n  return token >= tt.pipeline && token <= tt._instanceof;\n}\n\nexport function tokenIsPostfix(token: TokenType): boolean {\n  return token === tt.incDec;\n}\n\nexport function tokenIsPrefix(token: TokenType): boolean {\n  return tokenPrefixes[token];\n}\n\nexport function tokenIsTSTypeOperator(token: TokenType): boolean {\n  return token >= tt._keyof && token <= tt._unique;\n}\n\nexport function tokenIsTSDeclarationStart(token: TokenType): boolean {\n  return token >= tt._abstract && token <= tt._type;\n}\n\nexport function tokenLabelName(token: TokenType): string {\n  return tokenLabels[token];\n}\n\nexport function tokenOperatorPrecedence(token: TokenType): number {\n  return tokenBinops[token];\n}\n\nexport function tokenIsBinaryOperator(token: TokenType): boolean {\n  return tokenBinops[token] !== -1;\n}\n\nexport function tokenIsRightAssociative(token: TokenType): boolean {\n  return token === tt.exponent;\n}\n\nexport function tokenIsTemplate(token: TokenType): boolean {\n  return token >= tt.templateTail && token <= tt.templateNonTail;\n}\n\nexport function getExportedToken(token: TokenType): ExportedTokenType {\n  return tokenTypes[token];\n}\n\nexport function isTokenType(obj: any): boolean {\n  return typeof obj === \"number\";\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  tokenTypes[tt.braceR].updateContext = context => {\n    context.pop();\n  };\n\n  tokenTypes[tt.braceL].updateContext =\n    tokenTypes[tt.braceHashL].updateContext =\n    tokenTypes[tt.dollarBraceL].updateContext =\n      context => {\n        context.push(tc.brace);\n      };\n\n  tokenTypes[tt.backQuote].updateContext = context => {\n    if (context[context.length - 1] === tc.template) {\n      context.pop();\n    } else {\n      context.push(tc.template);\n    }\n  };\n\n  tokenTypes[tt.jsxTagStart].updateContext = context => {\n    context.push(tc.j_expr, tc.j_oTag);\n  };\n}\n","// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\n// ## Character categories\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point between 0x80 and 0xffff.\n// Generated by `scripts/generate-identifier-regex.cjs`.\n\n/* prettier-ignore */\nlet nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u0870-\\u0887\\u0889-\\u088e\\u08a0-\\u08c9\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c5d\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cdd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d04-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be