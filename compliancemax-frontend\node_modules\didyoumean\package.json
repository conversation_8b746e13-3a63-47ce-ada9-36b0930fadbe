{"name": "didyoume<PERSON>", "version": "1.2.2", "description": "Match human-quality input to potential matches by edit distance.", "homepage": "https://github.com/dcporter/didyoumean.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dcporter.net/"}, "keywords": ["didyoume<PERSON>", "mean", "edit", "distance", "<PERSON><PERSON><PERSON><PERSON>"], "main": "./didYouMean-1.2.1.js", "repository": {"type": "git", "url": "https://github.com/dcporter/didyoumean.js.git"}, "bugs": {"url": "https://github.com/dcporter/didyoumean.js/issues"}, "license": "Apache-2.0"}